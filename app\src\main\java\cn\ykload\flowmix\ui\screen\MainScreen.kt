package cn.ykload.flowmix.ui.screen

import android.content.Context
import android.content.Intent
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.AutoFixHigh
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Description
import androidx.compose.material.icons.filled.FileOpen
import androidx.compose.material.icons.filled.Folder
import androidx.compose.material.icons.filled.IosShare
import androidx.compose.material.icons.filled.Timeline
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.material3.Switch
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import cn.ykload.flowmix.data.AutoEqData
import cn.ykload.flowmix.data.EqBand
import cn.ykload.flowmix.ui.component.BottomModalType
import cn.ykload.flowmix.ui.component.BottomToast
import cn.ykload.flowmix.ui.component.EqAdjusterCard
import cn.ykload.flowmix.ui.theme.LexendFontFamily
import cn.ykload.flowmix.utils.Constants
import cn.ykload.flowmix.viewmodel.MainViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MainScreen(
    viewModel: MainViewModel,
    permissionManager: cn.ykload.flowmix.permission.PermissionManager,
    frequencyResponseViewModel: cn.ykload.flowmix.viewmodel.FrequencyResponseViewModel,
    onNavigateToFrequencyResponse: () -> Unit,
    hasPageEntered: Boolean = false,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val frequencyResponseUiState by frequencyResponseViewModel.uiState.collectAsStateWithLifecycle()

    // 创建稳定的默认EQ数据
    val defaultEqData = remember { createDefaultEqData() }

    // 使用传入的页面进入状态

    // 当AutoEq数据变化时，更新FrequencyResponseViewModel
    LaunchedEffect(uiState.currentEqData) {
        frequencyResponseViewModel.setAutoEqData(uiState.currentEqData)
    }

    // 文件选择器 - 使用更兼容的方式
    val filePickerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == android.app.Activity.RESULT_OK) {
            result.data?.data?.let { uri ->
                try {
                    val inputStream = context.contentResolver.openInputStream(uri)
                    val fileName = cn.ykload.flowmix.utils.FileUtils.getFileName(context, uri) ?: "AutoEq.txt"
                    inputStream?.let {
                        viewModel.importAutoEqFile(uri, it, fileName)
                    }
                } catch (e: Exception) {
                    viewModel.onFileReadError("文件读取失败: ${e.message}")
                }
            }
        }
    }
    
    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(horizontal = 16.dp)
            .verticalScroll(rememberScrollState()),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {

            // 页面标题
            Text(
                text = "AutoEq",
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(vertical = 8.dp)
            )

            // 文件选择区域
            FileSelectionCard(
                selectedFileName = uiState.selectedFileName,
                hasEqData = uiState.hasEqData,
                onSelectFile = {
                    if (uiState.canSelectFile) {
                        // 创建文件选择Intent
                        val fileAccessHelper = cn.ykload.flowmix.file.FileAccessHelper(context)
                        val intent = fileAccessHelper.createFilePickerIntent()
                        filePickerLauncher.launch(intent)
                    } else {
                        // 请求权限
                        viewModel.requestPermissions()
                    }
                },
                onOpenSeeq = {
                    openSeeqApp(context)
                },
                onExport = viewModel::showExportDialog
            )
            
            // AutoEq Card - 可点击展开调节器
            AutoEqCard(
                eqData = uiState.currentEqData ?: defaultEqData,
                isLoudnessCompensationEnabled = uiState.isLoudnessCompensationEnabled,
                globalGain = uiState.globalGain,
                isExpanded = uiState.isAutoEqCardExpanded,
                visibleBandRange = uiState.visibleEqBandRange,
                hasPageEntered = hasPageEntered,
                onCardClick = viewModel::toggleAutoEqCardExpanded,
                onAdjustBand = viewModel::adjustEqBand,
                onSetBandGain = viewModel::setBandGain,
                onVisibleRangeChanged = viewModel::updateVisibleEqBandRange
            )

            // 频响曲线图 - 与频响页面同步
            FrequencyResponseSection(
                frequencyResponseUiState = frequencyResponseUiState,
                uiState = uiState,
                onNavigateToFrequencyResponse = onNavigateToFrequencyResponse,
                hasPageEntered = hasPageEntered,
                visibleBandRange = if (uiState.isAutoEqCardExpanded) uiState.visibleEqBandRange else null,
                // 曲线可见性切换回调
                onToggleOriginalCurveVisibility = viewModel::toggleOriginalCurveVisibility,
                onToggleAutoEqCurveVisibility = viewModel::toggleAutoEqCurveVisibility,
                onToggleTargetCurveVisibility = viewModel::toggleTargetCurveVisibility
            )

            // 控制按钮
            ControlButtons(
                isLoudnessCompensationEnabled = uiState.isLoudnessCompensationEnabled,
                currentEqData = uiState.currentEqData,
                globalGain = uiState.globalGain,
                isFlowEqProcessing = uiState.isFlowEqProcessing,
                frequencyResponseUiState = frequencyResponseUiState,
                onToggleLoudnessCompensation = viewModel::toggleLoudnessCompensation,
                onGlobalGainChange = viewModel::adjustGlobalGain,
                onGlobalGainReset = viewModel::resetGlobalGain,
                onFlowEq = { originalData, targetData ->
                    viewModel.performFlowEq(originalData, targetData)
                }
            )
            
            // 使用底部模态框显示错误消息
            BottomToast(
                isVisible = uiState.errorMessage != null,
                message = uiState.errorMessage ?: "",
                type = BottomModalType.ERROR,
                onDismiss = viewModel::clearError
            )

            // 使用底部模态框显示成功消息
            BottomToast(
                isVisible = uiState.successMessage != null,
                message = uiState.successMessage ?: "",
                type = BottomModalType.SUCCESS,
                onDismiss = viewModel::clearSuccess
            )

            // 导出对话框
            if (uiState.showExportDialog) {
                ExportDialog(
                    fileName = uiState.exportFileName,
                    isExporting = uiState.isExporting,
                    onFileNameChange = viewModel::updateExportFileName,
                    onConfirm = viewModel::exportAutoEqFile,
                    onDismiss = viewModel::hideExportDialog
                )
            }

            // FlowEq确认对话框
            if (uiState.showFlowEqConfirmDialog) {
                FlowEqConfirmDialog(
                    onConfirm = viewModel::confirmPerformFlowEq,
                    onDismiss = viewModel::dismissFlowEqConfirmDialog
                )
            }

            // 等响度补偿确认对话框
            if (uiState.showLoudnessCompensationConfirmDialog) {
                LoudnessCompensationConfirmDialog(
                    isCurrentlyEnabled = uiState.isLoudnessCompensationEnabled,
                    onConfirm = viewModel::confirmToggleLoudnessCompensation,
                    onDismiss = viewModel::dismissLoudnessCompensationConfirmDialog
                )
            }

            // 权限说明
            if (!uiState.hasAllPermissions) {
                PermissionCard(
                    audioPermissionGranted = uiState.hasAudioPermission,
                    bluetoothPermissionGranted = uiState.hasBluetoothPermission,
                    storagePermissionGranted = uiState.hasStoragePermission,
                    onRequestPermissions = { viewModel.requestPermissions() }
                )
            }

            // 底部padding
            Spacer(modifier = Modifier.height(24.dp))
        }
    }



@Composable
private fun FileSelectionCard(
    selectedFileName: String?,
    hasEqData: Boolean,
    onSelectFile: () -> Unit,
    onOpenSeeq: () -> Unit,
    onExport: () -> Unit
) {
    // 获取显示的文件名（去除.txt后缀）
    val displayFileName = selectedFileName?.let { fileName ->
        if (fileName.endsWith(".txt", ignoreCase = true)) {
            fileName.substring(0, fileName.length - 4)
        } else {
            fileName
        }
    }

    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(25.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = displayFileName ?: "选择AutoEq文件或直接开始调节吧~",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            Spacer(modifier = Modifier.height(12.dp))

            // 按钮行
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(12.dp),
                verticalAlignment = androidx.compose.ui.Alignment.CenterVertically
            ) {
                // 文件选择按钮（只显示图标）
                Button(
                    onClick = onSelectFile,
                    modifier = Modifier.wrapContentWidth(),
                    contentPadding = PaddingValues(12.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.FileOpen,
                        contentDescription = "选择AutoEq文件",
                        modifier = Modifier.size(20.dp)
                    )
                }

                // 去Seeq里寻觅按钮
                Button(
                    onClick = onOpenSeeq,
                    modifier = Modifier.wrapContentWidth(),
                    contentPadding = PaddingValues(horizontal = 16.dp, vertical = 4.dp)
                ) {
                    Text("去")
                    Spacer(modifier = Modifier.width(2.dp))
                    Icon(
                        painter = painterResource(id = cn.ykload.flowmix.R.drawable.ic_seeq),
                        contentDescription = null,
                        modifier = Modifier.size(36.dp)
                    )
                    Spacer(modifier = Modifier.width(2.dp))
                    Text("里寻觅")
                }

                // 导出按钮
                Button(
                    onClick = onExport,
                    enabled = hasEqData,
                    modifier = Modifier.wrapContentWidth(),
                    contentPadding = PaddingValues(12.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.IosShare,
                        contentDescription = null,
                        modifier = Modifier.size(20.dp)
                    )
                }
            }
        }
    }
}



@Composable
private fun ControlButtons(
    isLoudnessCompensationEnabled: Boolean,
    currentEqData: AutoEqData?,
    globalGain: Float,
    isFlowEqProcessing: Boolean,
    frequencyResponseUiState: cn.ykload.flowmix.viewmodel.FrequencyResponseUiState,
    onToggleLoudnessCompensation: () -> Unit,
    onGlobalGainChange: (Float) -> Unit,
    onGlobalGainReset: () -> Unit,
    onFlowEq: (cn.ykload.flowmix.data.MeasurementCondition, cn.ykload.flowmix.data.MeasurementCondition) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(25.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {

            // 等响度开关
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Text(
                            text = "等响度",
                            style = MaterialTheme.typography.bodyMedium,
                            fontWeight = FontWeight.Medium
                        )

                        // 显示补偿值
                        if (isLoudnessCompensationEnabled && currentEqData != null) {
                            val compensationValue = currentEqData.calculateLoudnessCompensation()
                            Text(
                                text = "${if (compensationValue >= 0) "+" else ""}${"%.1f".format(compensationValue)}dB",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.secondary,
                                fontWeight = FontWeight.Bold
                            )
                        }
                    }

                    Text(
                        text = "自动调节整体增益以保持响度与原音一致",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
                Switch(
                    checked = isLoudnessCompensationEnabled,
                    onCheckedChange = { onToggleLoudnessCompensation() }
                )
            }

            // 整体增益调节滑动条
            cn.ykload.flowmix.ui.component.GlobalGainSlider(
                value = globalGain,
                onValueChange = onGlobalGainChange,
                onReset = onGlobalGainReset,
                enabled = currentEqData != null
            )

            // FlowEq按钮 - 总是显示，但根据条件启用/禁用
            val canUseFlowEq = frequencyResponseUiState.currentMeasurementData != null &&
                              frequencyResponseUiState.currentTargetData != null

            Button(
                onClick = {
                    if (canUseFlowEq) {
                        val originalData = frequencyResponseUiState.currentMeasurementData!!
                        val targetData = frequencyResponseUiState.currentTargetData!!
                        onFlowEq(originalData, targetData)
                    }
                },
                enabled = canUseFlowEq && !isFlowEqProcessing,
                modifier = Modifier.fillMaxWidth(),
                colors = ButtonDefaults.buttonColors(
                    containerColor = if (canUseFlowEq) {
                        MaterialTheme.colorScheme.secondary
                    } else {
                        MaterialTheme.colorScheme.surfaceVariant
                    },
                    contentColor = if (canUseFlowEq) {
                        MaterialTheme.colorScheme.onSecondary
                    } else {
                        MaterialTheme.colorScheme.onSurfaceVariant
                    }
                )
            ) {
                if (isFlowEqProcessing) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(18.dp),
                        strokeWidth = 2.dp,
                        color = MaterialTheme.colorScheme.onSecondary
                    )
                } else {
                    Icon(
                        imageVector = Icons.Default.AutoFixHigh,
                        contentDescription = null,
                        modifier = Modifier.size(18.dp),
                        tint = if (canUseFlowEq) {
                            MaterialTheme.colorScheme.onSecondary
                        } else {
                            MaterialTheme.colorScheme.onSurfaceVariant
                        }
                    )
                }
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = when {
                        isFlowEqProcessing -> "拟合中..."
                        canUseFlowEq -> "FlowEq 一键拟合"
                        else -> "要先选目标曲线才能拟合哦"
                    }
                )
            }


        }
    }
}



@Composable
private fun PermissionCard(
    audioPermissionGranted: Boolean,
    bluetoothPermissionGranted: Boolean,
    storagePermissionGranted: Boolean,
    onRequestPermissions: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(25.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.secondaryContainer
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "权限设置",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = "应用需要以下权限才能正常工作：",
                style = MaterialTheme.typography.bodyMedium
            )
            Spacer(modifier = Modifier.height(12.dp))

            // 音频权限
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = "音频设置权限",
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Medium
                    )
                    Text(
                        text = "用于应用全局音频效果",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }

                if (audioPermissionGranted) {
                    Text(
                        text = "✓ 已授权",
                        color = MaterialTheme.colorScheme.primary,
                        style = MaterialTheme.typography.bodySmall
                    )
                } else {
                    Text(
                        text = "✗ 未授权",
                        color = MaterialTheme.colorScheme.error,
                        style = MaterialTheme.typography.bodySmall
                    )
                }
            }

            Spacer(modifier = Modifier.height(8.dp))

            // 蓝牙权限
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = "蓝牙权限",
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Medium
                    )
                    Text(
                        text = "用于检测蓝牙音频设备",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }

                if (bluetoothPermissionGranted) {
                    Text(
                        text = "✓ 已授权",
                        color = MaterialTheme.colorScheme.primary,
                        style = MaterialTheme.typography.bodySmall
                    )
                } else {
                    Text(
                        text = "✗ 未授权",
                        color = MaterialTheme.colorScheme.error,
                        style = MaterialTheme.typography.bodySmall
                    )
                }
            }

            Spacer(modifier = Modifier.height(8.dp))

            // 存储权限
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = "文件读取权限",
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Medium
                    )
                    Text(
                        text = "用于读取AutoEq文件",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }

                if (storagePermissionGranted) {
                    Text(
                        text = "✓ 已授权",
                        color = MaterialTheme.colorScheme.primary,
                        style = MaterialTheme.typography.bodySmall
                    )
                } else {
                    Text(
                        text = "✗ 未授权",
                        color = MaterialTheme.colorScheme.error,
                        style = MaterialTheme.typography.bodySmall
                    )
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // 统一的权限请求按钮
            if (!audioPermissionGranted || !bluetoothPermissionGranted || !storagePermissionGranted) {
                Button(
                    onClick = onRequestPermissions,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text("请求权限")
                }
            }
        }
    }
}

/**
 * 启动Seeq应用
 */
private fun openSeeqApp(context: Context) {
    val packageName = "cn.ykload.seeq"
    println("尝试启动Seeq应用，包名: $packageName")

    // 方法1：直接尝试启动，不检查是否安装
    try {
        val intent = Intent().apply {
            action = Intent.ACTION_MAIN
            addCategory(Intent.CATEGORY_LAUNCHER)
            setPackage(packageName)
            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
        }

        context.startActivity(intent)
        println("方法1成功：直接启动Seeq应用")
        return
    } catch (e: Exception) {
        println("方法1失败: ${e.message}")
    }

    // 方法2：使用具体的Activity名称（常见的主Activity名称）
    val commonActivityNames = listOf(
        "$packageName.MainActivity",
        "$packageName.ui.MainActivity",
        "$packageName.activity.MainActivity",
        "$packageName.SplashActivity",
        "$packageName.LauncherActivity"
    )

    for (activityName in commonActivityNames) {
        try {
            val intent = Intent().apply {
                setClassName(packageName, activityName)
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
            }

            context.startActivity(intent)
            println("方法2成功：通过Activity名称启动Seeq应用 ($activityName)")
            return
        } catch (e: Exception) {
            println("尝试Activity $activityName 失败: ${e.message}")
        }
    }

    // 方法3：使用应用商店链接作为备选
    try {
        val marketIntent = Intent(Intent.ACTION_VIEW).apply {
            data = android.net.Uri.parse("market://details?id=$packageName")
            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        }
        context.startActivity(marketIntent)
        println("方法3：打开应用商店中的Seeq页面")
    } catch (e: Exception) {
        println("所有方法都失败了: ${e.message}")
    }
}

/**
 * 频响曲线图区域
 */
@Composable
private fun FrequencyResponseSection(
    frequencyResponseUiState: cn.ykload.flowmix.viewmodel.FrequencyResponseUiState,
    uiState: cn.ykload.flowmix.viewmodel.MainUiState,
    onNavigateToFrequencyResponse: () -> Unit,
    hasPageEntered: Boolean = false,
    visibleBandRange: IntRange? = null,
    // 曲线可见性切换回调
    onToggleOriginalCurveVisibility: () -> Unit,
    onToggleAutoEqCurveVisibility: () -> Unit,
    onToggleTargetCurveVisibility: () -> Unit
) {
    // 检查是否有频响数据
    if (frequencyResponseUiState.currentMeasurementData != null) {
        Column {
            // 图例 - 总是显示
            Card(
                modifier = Modifier.fillMaxWidth(),
                shape = RoundedCornerShape(25.dp)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(12.dp),
                    horizontalArrangement = Arrangement.Center
                ) {
                    // 目标曲线图例（如果有选择）
                    if (frequencyResponseUiState.selectedTargetCurve != null) {
                        Row(
                            modifier = Modifier.clickable {
                                onToggleTargetCurveVisibility()
                            },
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Box(
                                modifier = Modifier
                                    .size(12.dp)
                                    .background(
                                        androidx.compose.ui.graphics.Color.White.copy(
                                            alpha = if (uiState.isTargetCurveVisible) 1f else 0.3f
                                        ),
                                        RoundedCornerShape(25.dp)
                                    )
                            )
                            Spacer(modifier = Modifier.width(4.dp))
                            Text(
                                text = "目标曲线",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant.copy(
                                    alpha = if (uiState.isTargetCurveVisible) 1f else 0.5f
                                )
                            )
                        }

                        Spacer(modifier = Modifier.width(16.dp))
                    }

                    // 原始频响图例 - 总是显示
                    Row(
                        modifier = Modifier.clickable {
                            onToggleOriginalCurveVisibility()
                        },
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Box(
                            modifier = Modifier
                                .size(12.dp)
                                .background(
                                    MaterialTheme.colorScheme.secondary.copy(
                                        alpha = if (uiState.isOriginalCurveVisible) 1f else 0.3f
                                    ),
                                    RoundedCornerShape(25.dp)
                                )
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text(
                            text = "原始频响",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant.copy(
                                alpha = if (uiState.isOriginalCurveVisible) 1f else 0.5f
                            )
                        )
                    }

                    // AutoEq调整后图例（如果有AutoEq数据）
                    if (frequencyResponseUiState.autoEqData != null) {
                        Spacer(modifier = Modifier.width(16.dp))

                        Row(
                            modifier = Modifier.clickable {
                                onToggleAutoEqCurveVisibility()
                            },
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Box(
                                modifier = Modifier
                                    .size(12.dp)
                                    .background(
                                        MaterialTheme.colorScheme.primary.copy(
                                            alpha = if (uiState.isAutoEqCurveVisible) 1f else 0.3f
                                        ),
                                        RoundedCornerShape(25.dp)
                                    )
                            )
                            Spacer(modifier = Modifier.width(4.dp))
                            Text(
                                text = "AutoEq调整后",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant.copy(
                                    alpha = if (uiState.isAutoEqCurveVisible) 1f else 0.5f
                                )
                            )
                        }
                    }
                }
            }

            Spacer(modifier = Modifier.height(8.dp))

            // 频响曲线图表 - 占满Card
            cn.ykload.flowmix.ui.component.FrequencyResponseChart(
                measurementData = frequencyResponseUiState.currentMeasurementData,
                autoEqData = frequencyResponseUiState.autoEqData,
                targetData = frequencyResponseUiState.currentTargetData,
                isLoudnessCompensationEnabled = uiState.isLoudnessCompensationEnabled,
                globalGain = uiState.globalGain,
                visibleBandRange = visibleBandRange,
                modifier = Modifier.fillMaxWidth(),
                height = 220.dp,
                showChartLabel = true,
                chartLabelText = "Frequency Response",
                onPageEntered = hasPageEntered,
                // 曲线可见性状态
                isOriginalCurveVisible = uiState.isOriginalCurveVisible,
                isAutoEqCurveVisible = uiState.isAutoEqCurveVisible,
                isTargetCurveVisible = uiState.isTargetCurveVisible,
                onClick = onNavigateToFrequencyResponse
            )
        }
    } else {
        // 显示提示信息和跳转按钮
        Card(
            modifier = Modifier.fillMaxWidth(),
            shape = RoundedCornerShape(25.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(220.dp)
                    .padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Icon(
                    imageVector = Icons.Default.Timeline,
                    contentDescription = null,
                    modifier = Modifier.size(48.dp),
                    tint = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.6f)
                )
                Spacer(modifier = Modifier.height(16.dp))
                Text(
                    text = "前往「频响」页面选择耳机",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Spacer(modifier = Modifier.height(12.dp))
                Button(
                    onClick = onNavigateToFrequencyResponse,
                    modifier = Modifier.wrapContentWidth()
                ) {
                    Icon(
                        imageVector = Icons.Default.Timeline,
                        contentDescription = null,
                        modifier = Modifier.size(18.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("前往频响页面")
                }
            }
        }
    }
}

/**
 * AutoEq Card组件 - 包含图表和调节器
 */
@Composable
private fun AutoEqCard(
    eqData: AutoEqData,
    isLoudnessCompensationEnabled: Boolean,
    globalGain: Float,
    isExpanded: Boolean,
    visibleBandRange: IntRange?,
    hasPageEntered: Boolean,
    onCardClick: () -> Unit,
    onAdjustBand: (Float, Float) -> Unit,
    onSetBandGain: (Float, Float) -> Unit,
    onVisibleRangeChanged: (IntRange?) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        // EQ曲线图Card - 保持原始样式
        Card(
            modifier = Modifier.fillMaxWidth(),
            shape = RoundedCornerShape(25.dp)
        ) {
            // EQ曲线图 - 保持原来的样式，通过图表点击来展开/收起
            cn.ykload.flowmix.ui.component.EqCurveChart(
                eqData = eqData,
                isLoudnessCompensationEnabled = isLoudnessCompensationEnabled,
                globalGain = globalGain,
                visibleBandRange = if (isExpanded) visibleBandRange else null,
                modifier = Modifier.fillMaxWidth(),
                height = 220.dp,
                showChartLabel = true,
                chartLabelText = "AutoEq",
                onPageEntered = hasPageEntered,
                onChartClick = onCardClick
            )
        }

        // EQ调节器Card
        EqAdjusterCard(
            eqData = eqData,
            isExpanded = isExpanded,
            onAdjustBand = onAdjustBand,
            onSetBandGain = onSetBandGain,
            onVisibleRangeChanged = onVisibleRangeChanged
        )
    }
}

/**
 * 创建默认的EQ数据（127个频段，增益都为0）
 */
private fun createDefaultEqData(): AutoEqData {
    val defaultBands = Constants.AutoEqFormat.STANDARD_FREQUENCIES.map { frequency ->
        EqBand(frequency = frequency, gain = 0f)
    }
    return AutoEqData(bands = defaultBands, name = "默认EQ")
}

/**
 * 导出对话框组件
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun ExportDialog(
    fileName: String,
    isExporting: Boolean,
    onFileNameChange: (String) -> Unit,
    onConfirm: () -> Unit,
    onDismiss: () -> Unit
) {
    Dialog(onDismissRequest = { if (!isExporting) onDismiss() }) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(25.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface
            ),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
        ) {
            Column(
                modifier = Modifier.padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(20.dp)
            ) {
                // 关闭按钮
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End
                ) {
                    IconButton(
                        onClick = { if (!isExporting) onDismiss() },
                        modifier = Modifier.size(28.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Close,
                            contentDescription = "关闭",
                            tint = MaterialTheme.colorScheme.onSurfaceVariant,
                            modifier = Modifier.size(20.dp)
                        )
                    }
                }

                // 标题和图标
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    Box(
                        modifier = Modifier
                            .size(64.dp)
                            .background(
                                MaterialTheme.colorScheme.primaryContainer,
                                CircleShape
                            ),
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            imageVector = Icons.Default.IosShare,
                            contentDescription = null,
                            modifier = Modifier.size(32.dp),
                            tint = MaterialTheme.colorScheme.primary
                        )
                    }

                    Text(
                        text = "导出 AutoEq 文件",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onSurface
                    )

                    Text(
                        text = "输入文件名并导出您的音频均衡配置",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        textAlign = TextAlign.Center
                    )
                }

                // 导出表单
                Column(
                    verticalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    OutlinedTextField(
                        value = fileName,
                        onValueChange = onFileNameChange,
                        label = { Text("文件名") },
                        placeholder = { Text("请输入文件名") },
                        singleLine = true,
                        enabled = !isExporting,
                        modifier = Modifier.fillMaxWidth(),
                        shape = RoundedCornerShape(25.dp),
                        leadingIcon = {
                            Icon(
                                imageVector = Icons.Default.Description,
                                contentDescription = null,
                                tint = MaterialTheme.colorScheme.primary
                            )
                        },
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = MaterialTheme.colorScheme.primary,
                            focusedLabelColor = MaterialTheme.colorScheme.primary
                        )
                    )

                    // 保存路径提示
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.surfaceVariant
                        ),
                        shape = RoundedCornerShape(25.dp)
                    ) {
                        Row(
                            modifier = Modifier.padding(12.dp),
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.Folder,
                                contentDescription = null,
                                tint = MaterialTheme.colorScheme.onSurfaceVariant,
                                modifier = Modifier.size(16.dp)
                            )
                            Text(
                                text = "保存到：Documents/Flowmix/AutoEq/",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }

                    // 导出按钮
                    Button(
                        onClick = onConfirm,
                        enabled = !isExporting && fileName.trim().isNotEmpty(),
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(48.dp),
                        shape = RoundedCornerShape(25.dp),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = MaterialTheme.colorScheme.primary
                        )
                    ) {
                        if (isExporting) {
                            CircularProgressIndicator(
                                modifier = Modifier.size(20.dp),
                                strokeWidth = 2.dp,
                                color = MaterialTheme.colorScheme.onPrimary
                            )
                            Spacer(modifier = Modifier.width(12.dp))
                            Text(
                                "导出中...",
                                style = MaterialTheme.typography.titleMedium
                            )
                        } else {
                            Icon(
                                imageVector = Icons.Default.IosShare,
                                contentDescription = null,
                                modifier = Modifier.size(20.dp)
                            )
                            Spacer(modifier = Modifier.width(12.dp))
                            Text(
                                "导出文件",
                                style = MaterialTheme.typography.titleMedium
                            )
                        }
                    }
                }
            }
        }
    }
}

/**
 * FlowEq确认对话框
 */
@Composable
private fun FlowEqConfirmDialog(
    onConfirm: () -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "FlowEq",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold,
                fontFamily = LexendFontFamily
            )
        },
        text = {
            Column {
                Text(
                    text = "Flowmix 将根据选择的频响数据和目标曲线自动生成 AutoEq 配置。",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "此操作将覆盖当前的 AutoEq 配置，您可以zai",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.error,
                    fontWeight = FontWeight.Medium
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "确定要继续吗？",
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        },
        confirmButton = {
            Button(
                onClick = onConfirm,
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.colorScheme.primary
                )
            ) {
                Text("确定拟合")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}

/**
 * 等响度补偿确认对话框
 */
@Composable
private fun LoudnessCompensationConfirmDialog(
    isCurrentlyEnabled: Boolean,
    onConfirm: () -> Unit,
    onDismiss: () -> Unit
) {
    val actionText = if (isCurrentlyEnabled) "关闭" else "开启"
    val warningText = if (isCurrentlyEnabled) {
        "关闭等响度补偿后，整体增益将重置为 0dB"
    } else {
        "开启等响度补偿后，系统将自动调整整体增益以保持响度一致(若您当前增益较低，请小心炸音/失真)"
    }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "${actionText}等响度？",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold,
                fontFamily = LexendFontFamily
            )
        },
        text = {
            Column {
                Text(
                    text = "等响度功能可以自动调整 AutoEq 的整体增益，使调整后的声音响度与原始声音保持一致。",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "$warningText",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.error,
                    fontWeight = FontWeight.Medium
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "确定要${actionText}等响度吗？",
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        },
        confirmButton = {
            Button(
                onClick = onConfirm,
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.colorScheme.primary
                )
            ) {
                Text("确定${actionText}")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}

